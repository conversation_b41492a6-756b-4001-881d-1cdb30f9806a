"""
Workflow service layer
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.workflow import Workflow, WorkflowStatus
from app.schemas.workflow import WorkflowCreate, WorkflowUpdate


class WorkflowService:
    """Service for workflow operations"""
    
    @staticmethod
    def create_workflow(db: Session, workflow_data: WorkflowCreate) -> Workflow:
        """Create a new workflow"""
        db_workflow = Workflow(**workflow_data.model_dump())
        db.add(db_workflow)
        db.commit()
        db.refresh(db_workflow)
        return db_workflow
    
    @staticmethod
    def get_workflow(db: Session, workflow_id: int) -> Optional[Workflow]:
        """Get workflow by ID"""
        return db.query(Workflow).filter(Workflow.id == workflow_id).first()
    
    @staticmethod
    def get_workflows(db: Session, skip: int = 0, limit: int = 100) -> List[Workflow]:
        """Get list of workflows"""
        return db.query(Workflow).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_workflows_count(db: Session) -> int:
        """Get total count of workflows"""
        return db.query(Workflow).count()
    
    @staticmethod
    def update_workflow(db: Session, workflow_id: int, workflow_data: WorkflowUpdate) -> Optional[Workflow]:
        """Update workflow"""
        db_workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
        if not db_workflow:
            return None
        
        update_data = workflow_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_workflow, field, value)
        
        db.commit()
        db.refresh(db_workflow)
        return db_workflow
    
    @staticmethod
    def delete_workflow(db: Session, workflow_id: int) -> bool:
        """Delete workflow"""
        db_workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
        if not db_workflow:
            return False
        
        db.delete(db_workflow)
        db.commit()
        return True
