"""
API v1 Router
"""
from fastapi import APIRouter
from app.api.api_v1.endpoints import workflows, tables, fields, generation

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(workflows.router, prefix="/workflows", tags=["workflows"])
api_router.include_router(tables.router, prefix="/tables", tags=["tables"])
api_router.include_router(fields.router, prefix="/fields", tags=["fields"])
api_router.include_router(generation.router, prefix="/generation", tags=["generation"])

@api_router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "DatArk API v1 is running"}
