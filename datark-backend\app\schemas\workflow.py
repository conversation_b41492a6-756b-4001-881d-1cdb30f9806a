"""
Workflow related schemas
"""
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from app.models.workflow import WorkflowStatus


class WorkflowBase(BaseModel):
    """Base workflow schema"""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None


class WorkflowCreate(WorkflowBase):
    """Schema for creating workflow"""
    pass


class WorkflowUpdate(BaseModel):
    """Schema for updating workflow"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    status: Optional[WorkflowStatus] = None


class WorkflowInDB(WorkflowBase):
    """Schema for workflow in database"""
    id: int
    status: WorkflowStatus
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class Workflow(WorkflowInDB):
    """Complete workflow schema with relationships"""
    pass


class WorkflowList(BaseModel):
    """Schema for workflow list response"""
    workflows: List[WorkflowInDB]
    total: int
