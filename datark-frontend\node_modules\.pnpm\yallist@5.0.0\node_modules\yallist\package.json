{"name": "yallist", "version": "5.0.0", "description": "Yet Another Linked List", "files": ["dist"], "devDependencies": {"prettier": "^3.2.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn --ignore-path ../../.prettierignore --cache", "typedoc": "typedoc"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "BlueOak-1.0.0", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "engines": {"node": ">=18"}}