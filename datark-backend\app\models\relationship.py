"""
Table relationship models
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, Foreign<PERSON>ey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy import DateTime
from app.core.database import Base
import enum


class RelationshipType(str, enum.Enum):
    """Relationship types between tables"""
    ONE_TO_ONE = "1:1"
    ONE_TO_MANY = "1:n"
    MANY_TO_MANY = "n:m"


class TableRelationship(Base):
    """Table relationship model"""
    __tablename__ = "table_relationships"
    
    id = Column(Integer, primary_key=True, index=True)
    relationship_type = Column(Enum(RelationshipType), nullable=False)
    description = Column(Text, nullable=True)  # Natural language description
    source_table_id = Column(Integer, ForeignKey("data_tables.id"), nullable=False)
    target_table_id = Column(Integer, ForeignKey("data_tables.id"), nullable=False)
    workflow_id = Column(<PERSON><PERSON>ger, Foreign<PERSON>ey("workflows.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    workflow = relationship("Workflow", back_populates="relationships")
    source_table = relationship("DataTable", foreign_keys=[source_table_id], back_populates="source_relationships")
    target_table = relationship("DataTable", foreign_keys=[target_table_id], back_populates="target_relationships")
