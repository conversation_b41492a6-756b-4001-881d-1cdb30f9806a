"""
Workflow API endpoints
"""
from typing import List
from fastapi import API<PERSON>outer, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.schemas.workflow import Workflow, WorkflowCreate, WorkflowUpdate, WorkflowList
from app.services.workflow_service import WorkflowService

router = APIRouter()


@router.post("/", response_model=Workflow, status_code=status.HTTP_201_CREATED)
async def create_workflow(
    workflow_data: WorkflowCreate,
    db: Session = Depends(get_db)
):
    """Create a new workflow"""
    return WorkflowService.create_workflow(db, workflow_data)


@router.get("/", response_model=WorkflowList)
async def get_workflows(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get list of workflows"""
    workflows = WorkflowService.get_workflows(db, skip=skip, limit=limit)
    total = WorkflowService.get_workflows_count(db)
    return WorkflowList(workflows=workflows, total=total)


@router.get("/{workflow_id}", response_model=Workflow)
async def get_workflow(
    workflow_id: int,
    db: Session = Depends(get_db)
):
    """Get workflow by ID"""
    workflow = WorkflowService.get_workflow(db, workflow_id)
    if not workflow:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow not found"
        )
    return workflow


@router.put("/{workflow_id}", response_model=Workflow)
async def update_workflow(
    workflow_id: int,
    workflow_data: WorkflowUpdate,
    db: Session = Depends(get_db)
):
    """Update workflow"""
    workflow = WorkflowService.update_workflow(db, workflow_id, workflow_data)
    if not workflow:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow not found"
        )
    return workflow


@router.delete("/{workflow_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_workflow(
    workflow_id: int,
    db: Session = Depends(get_db)
):
    """Delete workflow"""
    success = WorkflowService.delete_workflow(db, workflow_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow not found"
        )
