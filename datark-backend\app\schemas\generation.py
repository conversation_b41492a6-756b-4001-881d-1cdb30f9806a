"""
Data generation related schemas
"""
from typing import Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime
from app.models.generation import GenerationStatus


class GenerationRequest(BaseModel):
    """Schema for data generation request"""
    record_counts: Dict[str, int]  # {"table_name": count}


class GenerationTaskInDB(BaseModel):
    """Schema for generation task in database"""
    id: int
    workflow_id: int
    status: GenerationStatus
    record_counts: Optional[Dict[str, int]] = None
    generated_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class GenerationTask(GenerationTaskInDB):
    """Complete generation task schema"""
    pass


class GenerationResult(BaseModel):
    """Schema for generation result"""
    task_id: int
    status: GenerationStatus
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
