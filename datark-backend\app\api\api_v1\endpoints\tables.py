"""
Data table API endpoints
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.schemas.table import DataTable, DataTableCreate, DataTableUpdate
from app.services.table_service import TableService

router = APIRouter()


@router.post("/", response_model=DataTable, status_code=status.HTTP_201_CREATED)
async def create_table(
    table_data: DataTableCreate,
    db: Session = Depends(get_db)
):
    """Create a new data table"""
    return TableService.create_table(db, table_data)


@router.get("/workflow/{workflow_id}", response_model=List[DataTable])
async def get_tables_by_workflow(
    workflow_id: int,
    db: Session = Depends(get_db)
):
    """Get all tables in a workflow"""
    return TableService.get_tables_by_workflow(db, workflow_id)


@router.get("/{table_id}", response_model=DataTable)
async def get_table(
    table_id: int,
    db: Session = Depends(get_db)
):
    """Get table by ID"""
    table = TableService.get_table(db, table_id)
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found"
        )
    return table


@router.put("/{table_id}", response_model=DataTable)
async def update_table(
    table_id: int,
    table_data: DataTableUpdate,
    db: Session = Depends(get_db)
):
    """Update table"""
    table = TableService.update_table(db, table_id, table_data)
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found"
        )
    return table


@router.delete("/{table_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_table(
    table_id: int,
    db: Session = Depends(get_db)
):
    """Delete table"""
    success = TableService.delete_table(db, table_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found"
        )
