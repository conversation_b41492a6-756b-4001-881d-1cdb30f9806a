"""
Table field API endpoints
"""
from typing import List
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.schemas.field import TableField, TableFieldCreate, TableFieldUpdate
from app.services.field_service import FieldService

router = APIRouter()


@router.post("/", response_model=TableField, status_code=status.HTTP_201_CREATED)
async def create_field(
    field_data: TableFieldCreate,
    db: Session = Depends(get_db)
):
    """Create a new table field"""
    return FieldService.create_field(db, field_data)


@router.get("/table/{table_id}", response_model=List[TableField])
async def get_fields_by_table(
    table_id: int,
    db: Session = Depends(get_db)
):
    """Get all fields in a table"""
    return FieldService.get_fields_by_table(db, table_id)


@router.get("/{field_id}", response_model=TableField)
async def get_field(
    field_id: int,
    db: Session = Depends(get_db)
):
    """Get field by ID"""
    field = FieldService.get_field(db, field_id)
    if not field:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Field not found"
        )
    return field


@router.put("/{field_id}", response_model=TableField)
async def update_field(
    field_id: int,
    field_data: TableFieldUpdate,
    db: Session = Depends(get_db)
):
    """Update field"""
    field = FieldService.update_field(db, field_id, field_data)
    if not field:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Field not found"
        )
    return field


@router.delete("/{field_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_field(
    field_id: int,
    db: Session = Depends(get_db)
):
    """Delete field"""
    success = FieldService.delete_field(db, field_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Field not found"
        )
