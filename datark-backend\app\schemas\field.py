"""
Table field related schemas
"""
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from app.models.table_field import FieldType


class TableFieldBase(BaseModel):
    """Base table field schema"""
    name: str = Field(..., min_length=1, max_length=255)
    field_type: FieldType
    ai_prompt: Optional[str] = None
    is_required: bool = False
    is_unique: bool = False
    is_primary_key: bool = False
    enum_options: Optional[str] = None  # JSON string
    default_value: Optional[str] = None
    order_index: int = 0


class TableFieldCreate(TableFieldBase):
    """Schema for creating table field"""
    table_id: int


class TableFieldUpdate(BaseModel):
    """Schema for updating table field"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    field_type: Optional[FieldType] = None
    ai_prompt: Optional[str] = None
    is_required: Optional[bool] = None
    is_unique: Optional[bool] = None
    is_primary_key: Optional[bool] = None
    enum_options: Optional[str] = None
    default_value: Optional[str] = None
    order_index: Optional[int] = None


class TableFieldInDB(TableFieldBase):
    """Schema for table field in database"""
    id: int
    table_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class TableField(TableFieldInDB):
    """Complete table field schema"""
    pass
