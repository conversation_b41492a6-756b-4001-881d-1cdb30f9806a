"""
Data table service layer
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.workflow import DataTable
from app.schemas.table import DataTableCreate, DataTableUpdate


class TableService:
    """Service for data table operations"""
    
    @staticmethod
    def create_table(db: Session, table_data: DataTableCreate) -> DataTable:
        """Create a new data table"""
        db_table = DataTable(**table_data.model_dump())
        db.add(db_table)
        db.commit()
        db.refresh(db_table)
        return db_table
    
    @staticmethod
    def get_table(db: Session, table_id: int) -> Optional[DataTable]:
        """Get data table by ID"""
        return db.query(DataTable).filter(DataTable.id == table_id).first()
    
    @staticmethod
    def get_tables_by_workflow(db: Session, workflow_id: int) -> List[DataTable]:
        """Get all tables in a workflow"""
        return db.query(DataTable).filter(DataTable.workflow_id == workflow_id).all()
    
    @staticmethod
    def update_table(db: Session, table_id: int, table_data: DataTableUpdate) -> Optional[DataTable]:
        """Update data table"""
        db_table = db.query(DataTable).filter(DataTable.id == table_id).first()
        if not db_table:
            return None
        
        update_data = table_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_table, field, value)
        
        db.commit()
        db.refresh(db_table)
        return db_table
    
    @staticmethod
    def delete_table(db: Session, table_id: int) -> bool:
        """Delete data table"""
        db_table = db.query(DataTable).filter(DataTable.id == table_id).first()
        if not db_table:
            return False
        
        db.delete(db_table)
        db.commit()
        return True
