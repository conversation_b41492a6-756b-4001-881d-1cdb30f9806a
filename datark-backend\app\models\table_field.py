"""
Table field related models
"""
from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, Text, Boolean, Foreign<PERSON>ey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy import DateTime
from app.core.database import Base
import enum


class FieldType(str, enum.Enum):
    """Field data types"""
    TEXT = "text"
    NUMBER = "number"
    DATE = "date"
    DATETIME = "datetime"
    BOOLEAN = "boolean"
    ENUM = "enum"
    LOCATION = "location"
    UUID = "uuid"
    EMAIL = "email"
    PHONE = "phone"
    URL = "url"


class TableField(Base):
    """Table field model"""
    __tablename__ = "table_fields"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)  # Field name
    field_type = Column(Enum(FieldType), nullable=False)
    ai_prompt = Column(Text, nullable=True)  # AI generation instruction
    is_required = Column(Boolean, default=False)
    is_unique = Column(Boolean, default=False)
    is_primary_key = Column(Boolean, default=False)
    enum_options = Column(Text, nullable=True)  # JSON string for enum options
    default_value = Column(String(255), nullable=True)
    table_id = Column(Integer, ForeignKey("data_tables.id"), nullable=False)
    order_index = Column(Integer, default=0)  # Field order in table
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    table = relationship("DataTable", back_populates="fields")
