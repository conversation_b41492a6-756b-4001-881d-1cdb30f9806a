"""
Workflow related models
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, Text, DateTime, Boolean, Foreign<PERSON>ey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class WorkflowStatus(str, enum.Enum):
    """Workflow execution status"""
    DRAFT = "draft"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class Workflow(Base):
    """Workflow model"""
    __tablename__ = "workflows"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    status = Column(Enum(WorkflowStatus), default=WorkflowStatus.DRAFT)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tables = relationship("DataTable", back_populates="workflow", cascade="all, delete-orphan")
    relationships = relationship("TableRelationship", back_populates="workflow", cascade="all, delete-orphan")
    generation_tasks = relationship("GenerationTask", back_populates="workflow", cascade="all, delete-orphan")


class DataTable(Base):
    """Data table model"""
    __tablename__ = "data_tables"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)  # Table name (English)
    alias = Column(String(255), nullable=True)  # Chinese alias
    workflow_id = Column(Integer, ForeignKey("workflows.id"), nullable=False)
    position_x = Column(Integer, default=0)  # Position in canvas
    position_y = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    workflow = relationship("Workflow", back_populates="tables")
    fields = relationship("TableField", back_populates="table", cascade="all, delete-orphan")
    source_relationships = relationship("TableRelationship", foreign_keys="TableRelationship.source_table_id", back_populates="source_table")
    target_relationships = relationship("TableRelationship", foreign_keys="TableRelationship.target_table_id", back_populates="target_table")
