"""
Data generation API endpoints
"""
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from app.core.database import get_db
from app.schemas.generation import GenerationRequest, GenerationResult, GenerationTask
from app.schemas.workflow import WorkflowUpdate
from app.services.deepseek_service import DeepSeekService
from app.models.generation import GenerationTask as GenerationTaskModel, GenerationStatus
from app.models.workflow import WorkflowStatus
from app.services.workflow_service import WorkflowService

router = APIRouter()


async def run_generation_task(db: Session, task_id: int, workflow_id: int, record_counts: dict):
    """Background task to run data generation"""
    deepseek_service = DeepSeekService()
    
    # Update task status to running
    task = db.query(GenerationTaskModel).filter(GenerationTaskModel.id == task_id).first()
    if task:
        task.status = GenerationStatus.RUNNING
        task.started_at = func.now()
        db.commit()
    
    try:
        # Generate data
        result = await deepseek_service.generate_data(db, workflow_id, record_counts)
        
        # Update task with results
        if task:
            task.status = GenerationStatus.COMPLETED
            task.generated_data = result["generated_data"]
            task.meta_prompt = result["meta_prompt"]
            task.ai_response = result["ai_response"]
            task.completed_at = func.now()
            db.commit()
        
        # Update workflow status
        update_data = WorkflowUpdate(status=WorkflowStatus.COMPLETED)
        WorkflowService.update_workflow(db, workflow_id, update_data)
        
    except Exception as e:
        # Update task with error
        if task:
            task.status = GenerationStatus.FAILED
            task.error_message = str(e)
            task.completed_at = func.now()
            db.commit()
        
        # Update workflow status
        update_data = WorkflowUpdate(status=WorkflowStatus.FAILED)
        WorkflowService.update_workflow(db, workflow_id, update_data)


@router.post("/workflows/{workflow_id}/generate", response_model=GenerationResult)
async def generate_data(
    workflow_id: int,
    request: GenerationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Start data generation for a workflow"""
    # Check if workflow exists
    workflow = WorkflowService.get_workflow(db, workflow_id)
    if not workflow:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow not found"
        )
    
    # Create generation task
    task = GenerationTaskModel(
        workflow_id=workflow_id,
        record_counts=request.record_counts,
        status=GenerationStatus.PENDING
    )
    db.add(task)
    db.commit()
    db.refresh(task)
    
    # Update workflow status
    update_data = WorkflowUpdate(status=WorkflowStatus.RUNNING)
    WorkflowService.update_workflow(db, workflow_id, update_data)
    
    # Start background task
    background_tasks.add_task(run_generation_task, db, task.id, workflow_id, request.record_counts)
    
    return GenerationResult(
        task_id=task.id,
        status=task.status
    )


@router.get("/tasks/{task_id}", response_model=GenerationTask)
async def get_generation_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """Get generation task status and results"""
    task = db.query(GenerationTaskModel).filter(GenerationTaskModel.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Generation task not found"
        )
    return task
