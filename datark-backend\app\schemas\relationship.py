"""
Table relationship related schemas
"""
from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime
from app.models.relationship import RelationshipType


class TableRelationshipBase(BaseModel):
    """Base table relationship schema"""
    relationship_type: RelationshipType
    description: Optional[str] = None
    source_table_id: int
    target_table_id: int


class TableRelationshipCreate(TableRelationshipBase):
    """Schema for creating table relationship"""
    workflow_id: int


class TableRelationshipUpdate(BaseModel):
    """Schema for updating table relationship"""
    relationship_type: Optional[RelationshipType] = None
    description: Optional[str] = None


class TableRelationshipInDB(TableRelationshipBase):
    """Schema for table relationship in database"""
    id: int
    workflow_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class TableRelationship(TableRelationshipInDB):
    """Complete table relationship schema"""
    pass
