"""
Table field service layer
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.table_field import TableField
from app.schemas.field import TableFieldCreate, TableFieldUpdate


class FieldService:
    """Service for table field operations"""
    
    @staticmethod
    def create_field(db: Session, field_data: TableFieldCreate) -> TableField:
        """Create a new table field"""
        db_field = TableField(**field_data.model_dump())
        db.add(db_field)
        db.commit()
        db.refresh(db_field)
        return db_field
    
    @staticmethod
    def get_field(db: Session, field_id: int) -> Optional[TableField]:
        """Get table field by ID"""
        return db.query(TableField).filter(TableField.id == field_id).first()
    
    @staticmethod
    def get_fields_by_table(db: Session, table_id: int) -> List[TableField]:
        """Get all fields in a table"""
        return db.query(TableField).filter(TableField.table_id == table_id).order_by(TableField.order_index).all()
    
    @staticmethod
    def update_field(db: Session, field_id: int, field_data: TableFieldUpdate) -> Optional[TableField]:
        """Update table field"""
        db_field = db.query(TableField).filter(TableField.id == field_id).first()
        if not db_field:
            return None
        
        update_data = field_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_field, field, value)
        
        db.commit()
        db.refresh(db_field)
        return db_field
    
    @staticmethod
    def delete_field(db: Session, field_id: int) -> bool:
        """Delete table field"""
        db_field = db.query(TableField).filter(TableField.id == field_id).first()
        if not db_field:
            return False
        
        db.delete(db_field)
        db.commit()
        return True
