"""
DeepSeek AI service for data generation
"""
import json
import httpx
from typing import Dict, Any, List
from sqlalchemy.orm import Session
from app.core.config import settings
from app.models.workflow import Workflow, DataTable
from app.models.table_field import TableField
from app.models.relationship import TableRelationship


class DeepSeekService:
    """Service for DeepSeek AI integration"""
    
    def __init__(self):
        self.api_key = settings.DEEPSEEK_API_KEY
        self.api_url = settings.DEEPSEEK_API_URL
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    async def generate_data(self, db: Session, workflow_id: int, record_counts: Dict[str, int]) -> Dict[str, Any]:
        """Generate data for a workflow"""
        # Get workflow with all related data
        workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
        if not workflow:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        # Build meta prompt
        meta_prompt = self._build_meta_prompt(db, workflow, record_counts)
        
        # Call DeepSeek API
        response_data = await self._call_deepseek_api(meta_prompt)
        
        # Parse and validate response
        generated_data = self._parse_response(response_data)
        
        return {
            "meta_prompt": meta_prompt,
            "ai_response": response_data,
            "generated_data": generated_data
        }
    
    def _build_meta_prompt(self, db: Session, workflow: Workflow, record_counts: Dict[str, int]) -> str:
        """Build meta prompt for AI"""
        # Get all tables with fields and relationships
        tables = db.query(DataTable).filter(DataTable.workflow_id == workflow.id).all()
        relationships = db.query(TableRelationship).filter(TableRelationship.workflow_id == workflow.id).all()
        
        prompt_parts = [
            f"# 数据生成任务：{workflow.name}",
            f"描述：{workflow.description or '无描述'}",
            "",
            "## 数据表结构定义",
        ]
        
        # Add table definitions
        for table in tables:
            fields = db.query(TableField).filter(TableField.table_id == table.id).order_by(TableField.order_index).all()
            
            prompt_parts.extend([
                f"### 表：{table.name} ({table.alias or table.name})",
                f"需要生成记录数：{record_counts.get(table.name, 10)}",
                "字段定义："
            ])
            
            for field in fields:
                field_desc = f"- {field.name} ({field.field_type.value})"
                if field.ai_prompt:
                    field_desc += f": {field.ai_prompt}"
                if field.is_required:
                    field_desc += " [必填]"
                if field.is_unique:
                    field_desc += " [唯一]"
                if field.is_primary_key:
                    field_desc += " [主键]"
                prompt_parts.append(field_desc)
            
            prompt_parts.append("")
        
        # Add relationships
        if relationships:
            prompt_parts.extend([
                "## 表关系定义",
            ])
            
            for rel in relationships:
                source_table = next((t for t in tables if t.id == rel.source_table_id), None)
                target_table = next((t for t in tables if t.id == rel.target_table_id), None)
                
                if source_table and target_table:
                    rel_desc = f"- {source_table.name} -> {target_table.name} ({rel.relationship_type.value})"
                    if rel.description:
                        rel_desc += f": {rel.description}"
                    prompt_parts.append(rel_desc)
        
        # Add generation instructions
        prompt_parts.extend([
            "",
            "## 生成要求",
            "1. 请根据上述表结构和关系生成符合逻辑的模拟数据",
            "2. 确保关系数据的一致性（如外键引用正确）",
            "3. 生成的数据应该真实、合理，符合业务场景",
            "4. 返回JSON格式，结构为：{\"table_name\": [{\"field\": \"value\", ...}, ...]}",
            "5. 请确保JSON格式正确，可以被解析"
        ])
        
        return "\n".join(prompt_parts)
    
    async def _call_deepseek_api(self, prompt: str) -> str:
        """Call DeepSeek API"""
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的数据生成助手，能够根据数据库表结构生成高质量的模拟数据。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 4000
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.api_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=60.0
            )
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
    
    def _parse_response(self, response_text: str) -> Dict[str, List[Dict[str, Any]]]:
        """Parse AI response to extract JSON data"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response_text[start_idx:end_idx]
            data = json.loads(json_str)
            
            return data
        except (json.JSONDecodeError, ValueError) as e:
            raise ValueError(f"Failed to parse AI response: {str(e)}")
