"""
Data generation related models
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, In<PERSON><PERSON>, String, Text, DateTime, ForeignKey, Enum, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class GenerationStatus(str, enum.Enum):
    """Data generation status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class GenerationTask(Base):
    """Data generation task model"""
    __tablename__ = "generation_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    workflow_id = Column(Integer, ForeignKey("workflows.id"), nullable=False)
    status = Column(Enum(GenerationStatus), default=GenerationStatus.PENDING)
    record_counts = Column(JSON, nullable=True)  # {"table_name": count} mapping
    generated_data = Column(JSON, nullable=True)  # Generated data result
    error_message = Column(Text, nullable=True)
    meta_prompt = Column(Text, nullable=True)  # The prompt sent to AI
    ai_response = Column(Text, nullable=True)  # Raw AI response
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    workflow = relationship("Workflow", back_populates="generation_tasks")
