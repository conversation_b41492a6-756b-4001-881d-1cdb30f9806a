"""
Data table related schemas
"""
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class DataTableBase(BaseModel):
    """Base data table schema"""
    name: str = Field(..., min_length=1, max_length=255)
    alias: Optional[str] = Field(None, max_length=255)
    position_x: int = 0
    position_y: int = 0


class DataTableCreate(DataTableBase):
    """Schema for creating data table"""
    workflow_id: int


class DataTableUpdate(BaseModel):
    """Schema for updating data table"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    alias: Optional[str] = Field(None, max_length=255)
    position_x: Optional[int] = None
    position_y: Optional[int] = None


class DataTableInDB(DataTableBase):
    """Schema for data table in database"""
    id: int
    workflow_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class DataTable(DataTableInDB):
    """Complete data table schema"""
    pass
